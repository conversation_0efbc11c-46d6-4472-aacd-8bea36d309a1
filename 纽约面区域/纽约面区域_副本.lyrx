{"type": "CIMLayerDocument", "version": "3.4.0", "build": 55405, "layers": ["CIMPATH=__/________.json"], "layerDefinitions": [{"type": "CIMFeatureLayer", "name": "纽约面区域_副本", "uRI": "CIMPATH=__/________.json", "sourceModifiedTime": {"type": "TimeInstant"}, "useSourceMetadata": true, "description": "纽约面区域_副本", "layerElevation": {"type": "CIMLayerElevationSurface"}, "expanded": true, "layer3DProperties": {"type": "CIM3DLayerProperties", "castShadows": true, "isLayerLit": true, "layerFaceCulling": "None", "maxDistance": -1, "minDistance": -1, "preloadTextureCutoffHigh": 0, "preloadTextureCutoffLow": 0.25, "textureCutoffHigh": 0.25, "textureCutoffLow": 1, "useCompressedTextures": true, "verticalExaggeration": 1, "exaggerationMode": "ScaleZ", "verticalUnit": {"uwkid": 9001}, "lighting": "OneSideDataNormal", "optimizeMarkerTransparency": true}, "layerType": "Operational", "showLegends": true, "visibility": true, "displayCacheType": "Permanent", "maxDisplayCacheAge": 5, "showPopups": true, "serviceLayerID": -1, "refreshRate": -1, "refreshRateUnit": "esriTimeUnitsSeconds", "blendingMode": "Alpha", "allowDrapingOnIntegratedMesh": true, "autoGenerateFeatureTemplates": true, "featureElevationExpression": "Shape.Z", "featureTable": {"type": "CIMFeatureTable", "displayField": "Shape_Length", "editable": true, "dataConnection": {"type": "CIMStandardDataConnection", "workspaceConnectionString": "DATABASE=..\\..\\AISProject\\AISProject.gdb", "workspaceFactory": "FileGDB", "dataset": "纽约面区域_副本", "datasetType": "esriDTFeatureClass"}, "studyAreaSpatialRel": "esriSpatialRelUndefined", "searchOrder": "esriSearchOrderSpatial"}, "htmlPopupEnabled": true, "selectable": true, "featureCacheType": "Session", "displayFiltersType": "ByScale", "featureBlendingMode": "Alpha", "layerEffectsMode": "Layer", "labelClasses": [{"type": "CIMLabelClass", "expressionTitle": "自定义", "expression": "$feature.Shape_Length", "expressionEngine": "Arcade", "featuresToLabel": "AllVisibleFeatures", "maplexLabelPlacementProperties": {"type": "CIMMaplexLabelPlacementProperties", "featureType": "Polygon", "avoidPolygonHoles": true, "canOverrunFeature": true, "canPlaceLabelOutsidePolygon": true, "canRemoveOverlappingLabel": true, "canStackLabel": true, "centerLabelAnchorType": "Symbol", "connectionType": "Unambiguous", "constrainOffset": "NoConstraint", "contourAlignmentType": "Page", "contourLadderType": "Straight", "contourMaximumAngle": 90, "enableConnection": true, "featureWeight": 0, "fontHeightReductionLimit": 4, "fontHeightReductionStep": 0.5, "fontWidthReductionLimit": 90, "fontWidthReductionStep": 5, "graticuleAlignmentType": "Straight", "keyNumberGroupName": "默认", "labelBuffer": 15, "labelLargestPolygon": true, "labelPriority": -1, "labelStackingProperties": {"type": "CIMMaplexLabelStackingProperties", "stackAlignment": "ChooseBest", "maximumNumberOfLines": 3, "minimumNumberOfCharsPerLine": 3, "maximumNumberOfCharsPerLine": 24, "separators": [{"type": "CIMMaplexStackingSeparator", "separator": " ", "splitAfter": true}, {"type": "CIMMaplexStackingSeparator", "separator": ",", "visible": true, "splitAfter": true}], "trimStackingSeparators": true}, "lineFeatureType": "General", "linePlacementMethod": "OffsetCurvedFromLine", "maximumLabelOverrun": 80, "maximumLabelOverrunUnit": "Point", "measureFromClippedFeatureGeometry": true, "minimumFeatureSizeUnit": "Map", "multiPartOption": "OneLabelPerPart", "offsetAlongLineProperties": {"type": "CIMMaplexOffsetAlongLineProperties", "placementMethod": "BestPositionAlongLine", "labelAnchorPoint": "CenterOfLabel", "distanceUnit": "Percentage", "useLineDirection": true}, "pointExternalZonePriorities": {"type": "CIMMaplexExternalZonePriorities", "aboveLeft": 4, "aboveCenter": 2, "aboveRight": 1, "centerRight": 3, "belowRight": 5, "belowCenter": 7, "belowLeft": 8, "centerLeft": 6}, "pointPlacementMethod": "AroundPoint", "polygonAnchorPointType": "GeometricCenter", "polygonBoundaryWeight": 0, "polygonExternalZones": {"type": "CIMMaplexExternalZonePriorities", "aboveLeft": 4, "aboveCenter": 2, "aboveRight": 1, "centerRight": 3, "belowRight": 5, "belowCenter": 7, "belowLeft": 8, "centerLeft": 6}, "polygonFeatureType": "General", "polygonInternalZones": {"type": "CIMMaplexInternalZonePriorities", "center": 1}, "polygonPlacementMethod": "HorizontalInPolygon", "primaryOffset": 1, "primaryOffsetUnit": "Point", "removeAmbiguousLabels": "All", "removeExtraWhiteSpace": true, "repetitionIntervalUnit": "Point", "rotationProperties": {"type": "CIMMaplexRotationProperties", "rotationType": "Arithmetic", "alignmentType": "Straight"}, "secondaryOffset": 100, "secondaryOffsetUnit": "Percentage", "strategyPriorities": {"type": "CIMMaplexStrategyPriorities", "stacking": 1, "overrun": 2, "fontCompression": 3, "fontReduction": 4, "abbreviation": 5}, "thinningDistanceUnit": "Point", "truncationMarkerCharacter": ".", "truncationMinimumLength": 1, "truncationPreferredCharacters": "<PERSON><PERSON><PERSON>", "truncationExcludedCharacters": "0123456789", "polygonAnchorPointPerimeterInsetUnit": "Point"}, "name": "类 1", "priority": -1, "standardLabelPlacementProperties": {"type": "CIMStandardLabelPlacementProperties", "featureType": "Line", "featureWeight": "None", "labelWeight": "High", "numLabelsOption": "OneLabelPerName", "lineLabelPosition": {"type": "CIMStandardLineLabelPosition", "above": true, "inLine": true, "parallel": true}, "lineLabelPriorities": {"type": "CIMStandardLineLabelPriorities", "aboveStart": 3, "aboveAlong": 3, "aboveEnd": 3, "centerStart": 3, "centerAlong": 3, "centerEnd": 3, "belowStart": 3, "belowAlong": 3, "belowEnd": 3}, "pointPlacementMethod": "AroundPoint", "pointPlacementPriorities": {"type": "CIMStandardPointPlacementPriorities", "aboveLeft": 2, "aboveCenter": 2, "aboveRight": 1, "centerLeft": 3, "centerRight": 2, "belowLeft": 3, "belowCenter": 3, "belowRight": 2}, "rotationType": "Arithmetic", "polygonPlacementMethod": "AlwaysHorizontal"}, "textSymbol": {"type": "CIMSymbolReference", "symbol": {"type": "CIMTextSymbol", "blockProgression": "TTB", "depth3D": 1, "extrapolateBaselines": true, "fontEffects": "Normal", "fontEncoding": "Unicode", "fontFamilyName": "Sim<PERSON>un", "fontStyleName": "Regular", "fontType": "Unspecified", "haloSize": 1, "height": 10, "hinting": "<PERSON><PERSON><PERSON>", "horizontalAlignment": "Left", "kerning": true, "letterWidth": 100, "ligatures": true, "lineGapType": "ExtraLeading", "symbol": {"type": "CIMPolygonSymbol", "symbolLayers": [{"type": "CIMSolidFill", "enable": true, "color": {"type": "CIMRGBColor", "values": [0, 0, 0, 100]}}], "angleAlignment": "Map"}, "textCase": "Normal", "textDirection": "LTR", "verticalAlignment": "Bottom", "verticalGlyphOrientation": "Right", "wordSpacing": 100, "billboardMode3D": "FaceNearPlane"}}, "useCodedValue": true, "visibility": true, "iD": -1}], "renderer": {"type": "CIMSimpleRenderer", "sampleSize": 10000, "patch": "<PERSON><PERSON><PERSON>", "symbol": {"type": "CIMSymbolReference", "symbol": {"type": "CIMPolygonSymbol", "symbolLayers": [{"type": "CIMSolidStroke", "enable": true, "name": "New_b76050ba-5d19-4bee-a6c3-800c0b9963da", "capStyle": "Round", "joinStyle": "Round", "lineStyle3D": "Strip", "miterLimit": 10, "width": 0.4, "height3D": 1, "anchor3D": "Center", "color": {"type": "CIMRGBColor", "values": [0, 0, 0, 100]}}, {"type": "CIMSolidFill", "enable": true, "name": "New_b7f82c31-54d2-48d2-9e6c-79a7b4252913", "color": {"type": "CIMRGBColor", "values": [240, 204, 230, 100]}}], "angleAlignment": "Map"}}}, "scaleSymbols": true, "snappable": true}], "rGBColorProfile": "sRGB IEC61966-2.1", "cMYKColorProfile": "U.S. Web Coated (SWOP) v2"}